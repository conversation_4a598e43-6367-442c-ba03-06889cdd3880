@keyframes fadeIn {
    0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1); 
      }
      100% {
        transform: scale(1); 
      }
  }
  
  .animate-fadeIn {
    animation: fadeIn 3s ease-in-out infinite;
 
  }



  
  



  @keyframes fadeInLetter {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .letter {
    opacity: 0;
    animation: fadeInLetter 5s ease-out infinite;
  }