"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HomePageClient.tsx":
/*!***************************************!*\
  !*** ./components/HomePageClient.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePageClient: function() { return /* binding */ HomePageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ HomePageClient auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HomePageClient() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setIsLoaded(true);\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                duration: 0.5,\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const letterVariants = {\n        hidden: {\n            y: 50,\n            opacity: 0\n        },\n        visible: (i)=>({\n                y: 0,\n                opacity: 1,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.8,\n                    ease: \"easeOut\"\n                }\n            })\n    };\n    const floatingVariants = {\n        animate: {\n            y: [\n                -10,\n                10,\n                -10\n            ],\n            transition: {\n                duration: 3,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }\n    };\n    const text = \"Welcome!\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute w-2 h-2 bg-blue-400/20 rounded-full\",\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            opacity: [\n                                0,\n                                1,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: Math.random() * 10 + 10,\n                            repeat: Infinity,\n                            delay: Math.random() * 5\n                        },\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"fixed w-6 h-6 bg-blue-500/20 rounded-full pointer-events-none z-50 mix-blend-difference\",\n                animate: {\n                    x: mousePosition.x - 12,\n                    y: mousePosition.y - 12\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 500,\n                    damping: 28\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n                className: \"relative bg-white/80 backdrop-blur-md border-b border-gray-200 shadow-sm\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-center md:justify-between px-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-start mx-3 my-2 text-3xl md:text-4xl font-bold text-gray-800\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    width: \"40\",\n                                    height: \"40\",\n                                    src: \"https://img.icons8.com/ios-filled/50/wallet.png\",\n                                    alt: \"wallet icon\",\n                                    className: \"mr-3\",\n                                    animate: {\n                                        rotate: [\n                                            0,\n                                            5,\n                                            -5,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"XenoPay\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-between space-x-4 mt-2 md:mt-0 font-semibold text-lg md:text-xl\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-serif text-gray-600 hidden md:inline\",\n                                    children: \"Your Financial Partner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    className: \"hidden md:inline\",\n                                    width: \"30\",\n                                    height: \"30\",\n                                    src: \"https://img.icons8.com/doodle/48/handshake--v1.png\",\n                                    alt: \"handshake icon\",\n                                    variants: floatingVariants,\n                                    animate: \"animate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col md:flex-row items-center justify-between flex-grow px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex flex-col md:w-1/2 mb-8 md:mb-0 space-y-6\",\n                        variants: itemVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-800\",\n                                children: text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"inline-block\",\n                                        variants: letterVariants,\n                                        custom: index,\n                                        whileHover: {\n                                            scale: 1.2,\n                                            color: \"#3B82F6\",\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        },\n                                        children: char\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-2xl md:text-4xl font-bold text-gray-700 leading-tight\",\n                                variants: itemVariants,\n                                children: [\n                                    \"All Your Payments,\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"One Tap Away\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"font-serif text-lg md:text-xl text-gray-600 leading-relaxed\",\n                                variants: itemVariants,\n                                children: \"Experience seamless financial transactions with our cutting-edge payment platform. Secure, fast, and reliable.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                variants: itemVariants,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/api/auth/signin\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\"\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Get Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.svg, {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        animate: {\n                                                            x: [\n                                                                0,\n                                                                5,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1.5,\n                                                            repeat: Infinity\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"flex flex-wrap gap-3 mt-6\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        name: \"Secure\",\n                                        icon: \"\\uD83D\\uDD12\"\n                                    },\n                                    {\n                                        name: \"Fast\",\n                                        icon: \"⚡\"\n                                    },\n                                    {\n                                        name: \"Reliable\",\n                                        icon: \"✅\"\n                                    },\n                                    {\n                                        name: \"24/7 Support\",\n                                        icon: \"\\uD83D\\uDEDF\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"px-4 py-2 bg-white/60 backdrop-blur-sm border border-gray-200 rounded-full text-sm font-medium text-gray-700 flex items-center space-x-2\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n                                            borderColor: \"#3B82F6\",\n                                            y: -2\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"grid grid-cols-3 gap-4 mt-8 p-6 bg-white/40 backdrop-blur-sm rounded-2xl border border-gray-200\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        number: \"10K+\",\n                                        label: \"Users\"\n                                    },\n                                    {\n                                        number: \"₹50M+\",\n                                        label: \"Processed\"\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Uptime\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"text-center\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.8 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    delay: index * 0.5\n                                                },\n                                                children: stat.number\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, stat.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex items-center justify-center md:w-1/2\",\n                        variants: itemVariants,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"relative\",\n                            animate: {\n                                y: [\n                                    -5,\n                                    5,\n                                    -5\n                                ],\n                                rotate: [\n                                    0,\n                                    1,\n                                    -1,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl opacity-20\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/paytm-bg.webp\",\n                                    alt: \"Illustration representing payment services\",\n                                    width: 400,\n                                    height: 400,\n                                    className: \"relative z-10 max-w-full h-auto drop-shadow-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.footer, {\n                className: \"bg-white/80 backdrop-blur-md border-t border-gray-200 text-center p-6\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 max-w-6xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" XenoPay\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-serif text-gray-500 text-sm\",\n                                    children: \"All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-serif text-gray-600\",\n                                children: \"Made by Harsh Kumar Mishra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex justify-center space-x-4\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://github.com/Harshmishra001\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        height: \"32\",\n                                        viewBox: \"0 0 72 72\",\n                                        width: \"32\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            fill: \"none\",\n                                            fillRule: \"evenodd\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M36,72 L36,72 C55.882251,72 72,55.882251 72,36 L72,36 C72,16.117749 55.882251,-3.65231026e-15 36,0 L36,0 C16.117749,3.65231026e-15 -2.4348735e-15,16.117749 0,36 L0,36 C2.4348735e-15,55.882251 16.117749,72 36,72 Z\",\n                                                    fill: \"#3E75C3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M35.9985,12 C22.746,12 12,22.7870921 12,36.096644 C12,46.7406712 18.876,55.7718301 28.4145,58.9584121 C29.6145,59.1797862 30.0525,58.4358488 30.0525,57.7973276 C30.0525,57.2250681 30.0315,55.7100863 30.0195,53.6996482 C23.343,55.1558981 21.9345,50.4693938 21.9345,50.4693938 C20.844,47.6864054 19.2705,46.9454799 19.2705,46.9454799 C17.091,45.4500754 19.4355,45.4801943 19.4355,45.4801943 C21.843,45.6503662 23.1105,47.9634994 23.1105,47.9634994 C25.2525,51.6455377 28.728,50.5823398 30.096,49.9649018 C30.3135,48.4077535 30.9345,47.3460615 31.62,46.7436831 C26.2905,46.1352808 20.688,44.0691228 20.688,34.8361671 C20.688,32.2052792 21.6225,30.0547881 23.1585,28.3696344 C22.911,27.7597262 22.0875,25.3110578 23.3925,21.9934585 C23.3925,21.9934585 25.4085,21.3459017 29.9925,24.4632101 C31.908,23.9285993 33.96,23.6620468 36.0015,23.6515052 C38.04,23.6620468 40.0935,23.9285993 42.0105,24.4632101 C46.5915,21.3459017 48.603,21.9934585 48.603,21.9934585 C49.9125,25.3110578 49.089,27.7597262 48.8415,28.3696344 C50.3805,30.0547881 51.309,32.2052792 51.309,34.8361671 C51.309,44.0917119 45.6975,46.1292571 40.3515,46.7256117 C41.2125,47.4695491 41.9805,48.9393525 41.9805,51.1877301 C41.9805,54.4089489 41.9505,57.0067059 41.9505,57.7973276 C41.9505,58.4418726 42.3825,59.1918338 43.6005,58.9554002 C53.13,55.7627944 60,46.7376593 60,36.096644 C60,22.7870921 49.254,12 35.9985,12\",\n                                                    fill: \"#FFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://www.linkedin.com/in/harsh-mishra001/\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        enableBackground: \"new 0 0 32 32\",\n                                        height: \"32px\",\n                                        id: \"Layer_1\",\n                                        version: \"1.0\",\n                                        viewBox: \"0 0 32 32\",\n                                        width: \"32px\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    clipRule: \"evenodd\",\n                                                    cx: \"16\",\n                                                    cy: \"16\",\n                                                    fill: \"#007BB5\",\n                                                    fillRule: \"evenodd\",\n                                                    r: \"16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                            fill: \"#FFFFFF\",\n                                                            height: \"14\",\n                                                            width: \"4\",\n                                                            x: \"7\",\n                                                            y: \"11\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20.499,11c-2.791,0-3.271,1.018-3.499,2v-2h-4v14h4v-8c0-1.297,0.703-2,2-2c1.266,0,2,0.688,2,2v8h4v-7 C25,14,24.479,11,20.499,11z\",\n                                                            fill: \"#FFFFFF\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"9\",\n                                                            cy: \"8\",\n                                                            fill: \"#FFFFFF\",\n                                                            r: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePageClient, \"+iAxKq/g7R0ug7rIK5a7synY29A=\");\n_c = HomePageClient;\nvar _c;\n$RefreshReg$(_c, \"HomePageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomePageClient.tsx\n"));

/***/ })

});