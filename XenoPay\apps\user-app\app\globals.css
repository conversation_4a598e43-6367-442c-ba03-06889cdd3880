@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes fadeIn {
    0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1); 
      }
      100% {
        transform: scale(1); 
      }
  }
  
  .animate-fadeIn {
    animation: fadeIn 3s ease-in-out infinite;
  }


  @keyframes fadeIn2 {
    0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1); 
      }
      100% {
        transform: scale(1); 
      }
  }
  
  .animate-fadeIn2 {
    animation: fadeIn 5s ease-in infinite;
  }
  
  



  @keyframes fadeInLetter {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .letter {
    display: inline-block;
    opacity: 0;
    animation: fadeInLetter 3.5s ease-out infinite;
  }
  
  .letter:nth-child(1) {
    animation-delay: 0s;
  }
  .letter:nth-child(2) {
    animation-delay: 0.1s;
  }
  .letter:nth-child(3) {
    animation-delay: 0.1s;
  }
  .letter:nth-child(4) {
    animation-delay: 0.3s;
  }
  .letter:nth-child(5) {
    animation-delay: 0.4s;
  }
  .letter:nth-child(6) {
    animation-delay: 0.5s;
  }
  .letter:nth-child(7) {
    animation-delay: 0.6s;
  }
  