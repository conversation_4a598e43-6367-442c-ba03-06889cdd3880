/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?64b7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Ccomponents%5CHomePageClient.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Ccomponents%5CHomePageClient.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/HomePageClient.tsx */ \"(ssr)/./components/HomePageClient.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhbmpheU0lNUNEZXNrdG9wJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDMTAweGRldiU1Q0NvaG9ydCUyMDIuMCU1Q1dlYiUyMGNvZGVzJTVDeGVub3BheSU1Q1hlbm9QYXklNUNhcHBzJTVDdXNlci1hcHAlNUNjb21wb25lbnRzJTVDSG9tZVBhZ2VDbGllbnQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3VzZXItYXBwLz8xMWFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FuamF5TVxcXFxEZXNrdG9wXFxcXEhBUlNIICAoYnRlY2ggY3NlKVxcXFwxMDB4ZGV2XFxcXENvaG9ydCAyLjBcXFxcV2ViIGNvZGVzXFxcXHhlbm9wYXlcXFxcWGVub1BheVxcXFxhcHBzXFxcXHVzZXItYXBwXFxcXGNvbXBvbmVudHNcXFxcSG9tZVBhZ2VDbGllbnQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Ccomponents%5CHomePageClient.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Cprovider.tsx&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp%5Cglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Cprovider.tsx&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp%5Cglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./provider.tsx */ \"(ssr)/./provider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhbmpheU0lNUNEZXNrdG9wJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDMTAweGRldiU1Q0NvaG9ydCUyMDIuMCU1Q1dlYiUyMGNvZGVzJTVDeGVub3BheSU1Q1hlbm9QYXklNUNhcHBzJTVDdXNlci1hcHAlNUNwcm92aWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNTYW5qYXlNJTVDRGVza3RvcCU1Q0hBUlNIJTIwJTIwKGJ0ZWNoJTIwY3NlKSU1QzEwMHhkZXYlNUNDb2hvcnQlMjAyLjAlNUNXZWIlMjBjb2RlcyU1Q3hlbm9wYXklNUNYZW5vUGF5JTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2FuamF5TSU1Q0Rlc2t0b3AlNUNIQVJTSCUyMCUyMChidGVjaCUyMGNzZSklNUMxMDB4ZGV2JTVDQ29ob3J0JTIwMi4wJTVDV2ViJTIwY29kZXMlNUN4ZW5vcGF5JTVDWGVub1BheSU1Q2FwcHMlNUN1c2VyLWFwcCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3VzZXItYXBwLz8yZWQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FuamF5TVxcXFxEZXNrdG9wXFxcXEhBUlNIICAoYnRlY2ggY3NlKVxcXFwxMDB4ZGV2XFxcXENvaG9ydCAyLjBcXFxcV2ViIGNvZGVzXFxcXHhlbm9wYXlcXFxcWGVub1BheVxcXFxhcHBzXFxcXHVzZXItYXBwXFxcXHByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Cprovider.tsx&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/HomePageClient.tsx":
/*!***************************************!*\
  !*** ./components/HomePageClient.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePageClient: () => (/* binding */ HomePageClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ HomePageClient auto */ \n\n\n\n\nfunction HomePageClient() {\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                duration: 0.5,\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                duration: 0.6,\n                ease: [\n                    0.6,\n                    -0.05,\n                    0.01,\n                    0.99\n                ]\n            }\n        }\n    };\n    const letterVariants = {\n        hidden: {\n            y: 50,\n            opacity: 0\n        },\n        visible: (i)=>({\n                y: 0,\n                opacity: 1,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.8,\n                    ease: [\n                        0.6,\n                        -0.05,\n                        0.01,\n                        0.99\n                    ]\n                }\n            })\n    };\n    const text = \"Welcome!\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute w-2 h-2 bg-blue-400/20 rounded-full\",\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            opacity: [\n                                0,\n                                1,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: Math.random() * 10 + 10,\n                            repeat: Infinity,\n                            delay: Math.random() * 5\n                        },\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"fixed w-6 h-6 bg-blue-500/20 rounded-full pointer-events-none z-50 mix-blend-difference\",\n                animate: {\n                    x: mousePosition.x - 12,\n                    y: mousePosition.y - 12\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 500,\n                    damping: 28\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n                className: \"relative bg-white/80 backdrop-blur-md border-b border-gray-200 shadow-sm\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-center md:justify-between px-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-start mx-3 my-2 text-3xl md:text-4xl font-bold text-gray-800\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    width: \"40\",\n                                    height: \"40\",\n                                    src: \"https://img.icons8.com/ios-filled/50/wallet.png\",\n                                    alt: \"wallet icon\",\n                                    className: \"mr-3\",\n                                    animate: {\n                                        rotate: [\n                                            0,\n                                            5,\n                                            -5,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.6,\n                                            1\n                                        ]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"XenoPay\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-between space-x-4 mt-2 md:mt-0 font-semibold text-lg md:text-xl\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-serif text-gray-600 hidden md:inline\",\n                                    children: \"Your Financial Partner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    className: \"hidden md:inline\",\n                                    width: \"30\",\n                                    height: \"30\",\n                                    src: \"https://img.icons8.com/doodle/48/handshake--v1.png\",\n                                    alt: \"handshake icon\",\n                                    animate: {\n                                        y: [\n                                            -10,\n                                            10,\n                                            -10\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.6,\n                                            1\n                                        ]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col md:flex-row items-center justify-between flex-grow px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex flex-col md:w-1/2 mb-8 md:mb-0 space-y-6\",\n                        variants: itemVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-800\",\n                                children: text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"inline-block\",\n                                        variants: letterVariants,\n                                        custom: index,\n                                        whileHover: {\n                                            scale: 1.2,\n                                            color: \"#3B82F6\",\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        },\n                                        children: char\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-2xl md:text-4xl font-bold text-gray-700 leading-tight\",\n                                variants: itemVariants,\n                                children: [\n                                    \"All Your Payments,\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"One Tap Away\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"font-serif text-lg md:text-xl text-gray-600 leading-relaxed\",\n                                variants: itemVariants,\n                                children: \"Experience seamless financial transactions with our cutting-edge payment platform. Secure, fast, and reliable.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                variants: itemVariants,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/api/auth/signin\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\"\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Get Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.svg, {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        animate: {\n                                                            x: [\n                                                                0,\n                                                                5,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1.5,\n                                                            repeat: Infinity\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"flex flex-wrap gap-3 mt-6\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        name: \"Secure\",\n                                        icon: \"\\uD83D\\uDD12\"\n                                    },\n                                    {\n                                        name: \"Fast\",\n                                        icon: \"⚡\"\n                                    },\n                                    {\n                                        name: \"Reliable\",\n                                        icon: \"✅\"\n                                    },\n                                    {\n                                        name: \"24/7 Support\",\n                                        icon: \"\\uD83D\\uDEDF\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"px-4 py-2 bg-white/60 backdrop-blur-sm border border-gray-200 rounded-full text-sm font-medium text-gray-700 flex items-center space-x-2\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n                                            borderColor: \"#3B82F6\",\n                                            y: -2\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"grid grid-cols-3 gap-4 mt-8 p-6 bg-white/40 backdrop-blur-sm rounded-2xl border border-gray-200\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        number: \"10K+\",\n                                        label: \"Users\"\n                                    },\n                                    {\n                                        number: \"₹50M+\",\n                                        label: \"Processed\"\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Uptime\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"text-center\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.8 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    delay: index * 0.5\n                                                },\n                                                children: stat.number\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, stat.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex items-center justify-center md:w-1/2\",\n                        variants: itemVariants,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"relative\",\n                            animate: {\n                                y: [\n                                    -5,\n                                    5,\n                                    -5\n                                ],\n                                rotate: [\n                                    0,\n                                    1,\n                                    -1,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: [\n                                    0.4,\n                                    0,\n                                    0.6,\n                                    1\n                                ]\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl opacity-20\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/paytm-bg.webp\",\n                                    alt: \"Illustration representing payment services\",\n                                    width: 400,\n                                    height: 400,\n                                    className: \"relative z-10 max-w-full h-auto drop-shadow-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.footer, {\n                className: \"bg-white/80 backdrop-blur-md border-t border-gray-200 text-center p-6\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 max-w-6xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" XenoPay\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-serif text-gray-500 text-sm\",\n                                    children: \"All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-serif text-gray-600\",\n                                children: \"Made by Harsh Kumar Mishra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex justify-center space-x-4\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://github.com/Harshmishra001\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        height: \"32\",\n                                        viewBox: \"0 0 72 72\",\n                                        width: \"32\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            fill: \"none\",\n                                            fillRule: \"evenodd\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M36,72 L36,72 C55.882251,72 72,55.882251 72,36 L72,36 C72,16.117749 55.882251,-3.65231026e-15 36,0 L36,0 C16.117749,3.65231026e-15 -2.4348735e-15,16.117749 0,36 L0,36 C2.4348735e-15,55.882251 16.117749,72 36,72 Z\",\n                                                    fill: \"#3E75C3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M35.9985,12 C22.746,12 12,22.7870921 12,36.096644 C12,46.7406712 18.876,55.7718301 28.4145,58.9584121 C29.6145,59.1797862 30.0525,58.4358488 30.0525,57.7973276 C30.0525,57.2250681 30.0315,55.7100863 30.0195,53.6996482 C23.343,55.1558981 21.9345,50.4693938 21.9345,50.4693938 C20.844,47.6864054 19.2705,46.9454799 19.2705,46.9454799 C17.091,45.4500754 19.4355,45.4801943 19.4355,45.4801943 C21.843,45.6503662 23.1105,47.9634994 23.1105,47.9634994 C25.2525,51.6455377 28.728,50.5823398 30.096,49.9649018 C30.3135,48.4077535 30.9345,47.3460615 31.62,46.7436831 C26.2905,46.1352808 20.688,44.0691228 20.688,34.8361671 C20.688,32.2052792 21.6225,30.0547881 23.1585,28.3696344 C22.911,27.7597262 22.0875,25.3110578 23.3925,21.9934585 C23.3925,21.9934585 25.4085,21.3459017 29.9925,24.4632101 C31.908,23.9285993 33.96,23.6620468 36.0015,23.6515052 C38.04,23.6620468 40.0935,23.9285993 42.0105,24.4632101 C46.5915,21.3459017 48.603,21.9934585 48.603,21.9934585 C49.9125,25.3110578 49.089,27.7597262 48.8415,28.3696344 C50.3805,30.0547881 51.309,32.2052792 51.309,34.8361671 C51.309,44.0917119 45.6975,46.1292571 40.3515,46.7256117 C41.2125,47.4695491 41.9805,48.9393525 41.9805,51.1877301 C41.9805,54.4089489 41.9505,57.0067059 41.9505,57.7973276 C41.9505,58.4418726 42.3825,59.1918338 43.6005,58.9554002 C53.13,55.7627944 60,46.7376593 60,36.096644 C60,22.7870921 49.254,12 35.9985,12\",\n                                                    fill: \"#FFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://www.linkedin.com/in/harsh-mishra001/\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        enableBackground: \"new 0 0 32 32\",\n                                        height: \"32px\",\n                                        id: \"Layer_1\",\n                                        version: \"1.0\",\n                                        viewBox: \"0 0 32 32\",\n                                        width: \"32px\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    clipRule: \"evenodd\",\n                                                    cx: \"16\",\n                                                    cy: \"16\",\n                                                    fill: \"#007BB5\",\n                                                    fillRule: \"evenodd\",\n                                                    r: \"16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                            fill: \"#FFFFFF\",\n                                                            height: \"14\",\n                                                            width: \"4\",\n                                                            x: \"7\",\n                                                            y: \"11\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20.499,11c-2.791,0-3.271,1.018-3.499,2v-2h-4v14h4v-8c0-1.297,0.703-2,2-2c1.266,0,2,0.688,2,2v8h4v-7 C25,14,24.479,11,20.499,11z\",\n                                                            fill: \"#FFFFFF\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"9\",\n                                                            cy: \"8\",\n                                                            fill: \"#FFFFFF\",\n                                                            r: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/HomePageClient.tsx\n");

/***/ }),

/***/ "(ssr)/./provider.tsx":
/*!**********************!*\
  !*** ./provider.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var recoil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! recoil */ \"(ssr)/../../node_modules/recoil/es/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nconst Providers = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recoil__WEBPACK_IMPORTED_MODULE_1__.RecoilRoot, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\provider.tsx\",\n            lineNumber: 7,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 12\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNvQztBQUNjO0FBRTNDLE1BQU1FLFlBQVksQ0FBQyxFQUFDQyxRQUFRLEVBQThCO0lBQzdELHFCQUFPLDhEQUFDSCw4Q0FBVUE7a0JBQ2QsNEVBQUNDLDREQUFlQTtzQkFDWEU7Ozs7Ozs7Ozs7O0FBR2IsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3VzZXItYXBwLy4vcHJvdmlkZXIudHN4PzNhZDQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuaW1wb3J0IHsgUmVjb2lsUm9vdCB9IGZyb20gXCJyZWNvaWxcIjtcclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IFByb3ZpZGVycyA9ICh7Y2hpbGRyZW59OiB7Y2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZX0pID0+IHtcclxuICAgIHJldHVybiA8UmVjb2lsUm9vdD5cclxuICAgICAgICA8U2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgPC9TZXNzaW9uUHJvdmlkZXI+XHJcbiAgICA8L1JlY29pbFJvb3Q+XHJcbn0iXSwibmFtZXMiOlsiUmVjb2lsUm9vdCIsIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0b2469f88d70\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91c2VyLWFwcC8uL2FwcC9nbG9iYWxzLmNzcz8wMjdjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGIyNDY5Zjg4ZDcwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../provider */ \"(rsc)/./provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Wallet\",\n    description: \"Simple wallet app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-w-screen min-h-screen bg-[#ebe6e6]\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFNTUE7QUFOaUI7QUFHaUI7QUFLakMsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ1AsZ0RBQVNBO3NCQUNSLDRFQUFDUTtnQkFBS0MsV0FBV1YsMkpBQWU7MEJBQzlCLDRFQUFDVztvQkFBSUQsV0FBVTs4QkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdXNlci1hcHAvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSBcIi4uL3Byb3ZpZGVyXCI7XHJcbmltcG9ydCB7IEFwcGJhckNsaWVudCB9IGZyb20gXCIuLi9jb21wb25lbnRzL0FwcGJhckNsaWVudFwiO1xyXG5cclxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJXYWxsZXRcIixcclxuICBkZXNjcmlwdGlvbjogXCJTaW1wbGUgd2FsbGV0IGFwcFwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KTogSlNYLkVsZW1lbnQge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LXNjcmVlbiBtaW4taC1zY3JlZW4gYmctWyNlYmU2ZTZdXCI+XHJcbiAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvYm9keT5cclxuICAgICAgPC9Qcm92aWRlcnM+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiaW50ZXIiLCJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _repo_db_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @repo/db/client */ \"(rsc)/../../packages/db/src/index.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/../../node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"Credentials\",\n            credentials: {\n                phone: {\n                    label: \"Phone number\",\n                    type: \"text\",\n                    placeholder: \"**********\",\n                    required: true\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    required: true\n                }\n            },\n            async authorize (credentials) {\n                const existingUser = await _repo_db_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findFirst({\n                    where: {\n                        number: credentials.phone\n                    }\n                });\n                if (existingUser) {\n                    const passwordValidation = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, existingUser.password);\n                    if (passwordValidation) {\n                        return {\n                            id: existingUser.id.toString()\n                        };\n                    }\n                    return null;\n                }\n                try {\n                    const hashedPassword = await bcrypt__WEBPACK_IMPORTED_MODULE_2___default().hash(credentials.password, 10);\n                    const result = await _repo_db_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].$transaction(async (prisma)=>{\n                        const user = await prisma.user.create({\n                            data: {\n                                number: credentials.phone,\n                                password: hashedPassword\n                            }\n                        });\n                        await prisma.balance.create({\n                            data: {\n                                userId: user.id,\n                                amount: 0\n                            }\n                        });\n                        return user;\n                    });\n                    return {\n                        id: result.id.toString()\n                    };\n                } catch (e) {\n                    console.error(\"Error creating user or balance:\", e);\n                }\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    secret: process.env.JWT_SECRET || \"secret\",\n    callbacks: {\n        async session ({ token, session }) {\n            session.user.id = token.sub;\n            return session;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/../../node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_HomePageClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/HomePageClient */ \"(rsc)/./components/HomePageClient.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n\n\n\n\n\n\nasync function Page() {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    if (session?.user) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/account-details\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomePageClient__WEBPACK_IMPORTED_MODULE_3__.HomePageClient, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUM2QztBQUNGO0FBQ21CO0FBQ3ZDO0FBQ2tCO0FBRTFCLGVBQWVJO0lBQzVCLE1BQU1DLFVBQVUsTUFBTUwsMkRBQWdCQSxDQUFDRyxrREFBV0E7SUFFbEQsSUFBSUUsU0FBU0MsTUFBTTtRQUNqQkwseURBQVFBLENBQUM7SUFDWDtJQUVBLHFCQUFPLDhEQUFDQyxzRUFBY0E7Ozs7O0FBQ3hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdXNlci1hcHAvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gXCJuZXh0LWF1dGhcIjtcclxuaW1wb3J0IHsgcmVkaXJlY3QgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyBIb21lUGFnZUNsaWVudCB9IGZyb20gXCIuLi9jb21wb25lbnRzL0hvbWVQYWdlQ2xpZW50XCI7XHJcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSBcIi4vbGliL2F1dGhcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFBhZ2UoKSB7XHJcbiAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpO1xyXG5cclxuICBpZiAoc2Vzc2lvbj8udXNlcikge1xyXG4gICAgcmVkaXJlY3QoJy9hY2NvdW50LWRldGFpbHMnKTtcclxuICB9XHJcblxyXG4gIHJldHVybiA8SG9tZVBhZ2VDbGllbnQgLz47XHJcbn0iXSwibmFtZXMiOlsiZ2V0U2VydmVyU2Vzc2lvbiIsInJlZGlyZWN0IiwiSG9tZVBhZ2VDbGllbnQiLCJhdXRoT3B0aW9ucyIsIlBhZ2UiLCJzZXNzaW9uIiwidXNlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/HomePageClient.tsx":
/*!***************************************!*\
  !*** ./components/HomePageClient.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HomePageClient: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\HARSH  (btech cse)\100xdev\Cohort 2.0\Web codes\xenopay\XenoPay\apps\user-app\components\HomePageClient.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\HARSH  (btech cse)\100xdev\Cohort 2.0\Web codes\xenopay\XenoPay\apps\user-app\components\HomePageClient.tsx#HomePageClient`);


/***/ }),

/***/ "(rsc)/./provider.tsx":
/*!**********************!*\
  !*** ./provider.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\HARSH  (btech cse)\100xdev\Cohort 2.0\Web codes\xenopay\XenoPay\apps\user-app\provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\HARSH  (btech cse)\100xdev\Cohort 2.0\Web codes\xenopay\XenoPay\apps\user-app\provider.tsx#Providers`);


/***/ }),

/***/ "(rsc)/../../packages/db/src/index.ts":
/*!**************************************!*\
  !*** ../../packages/db/src/index.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prismaClientSingleton = ()=>{\n    return new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n};\nconst prisma = globalThis.prismaGlobal ?? prismaClientSingleton();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\nif (true) globalThis.prismaGlobal = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vcGFja2FnZXMvZGIvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyx3QkFBd0I7SUFDNUIsT0FBTyxJQUFJRCx3REFBWUE7QUFDekI7QUFNQSxNQUFNRSxTQUFtREMsV0FBV0MsWUFBWSxJQUFJSDtBQUVwRixpRUFBZUMsTUFBTUEsRUFBQTtBQUVyQixJQUFJRyxJQUF5QixFQUFjRixXQUFXQyxZQUFZLEdBQUdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdXNlci1hcHAvLi4vLi4vcGFja2FnZXMvZGIvc3JjL2luZGV4LnRzPzZhMDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXHJcblxyXG5jb25zdCBwcmlzbWFDbGllbnRTaW5nbGV0b24gPSAoKSA9PiB7XHJcbiAgcmV0dXJuIG5ldyBQcmlzbWFDbGllbnQoKVxyXG59XHJcblxyXG5kZWNsYXJlIGdsb2JhbCB7XHJcbiAgdmFyIHByaXNtYUdsb2JhbDogdW5kZWZpbmVkIHwgUmV0dXJuVHlwZTx0eXBlb2YgcHJpc21hQ2xpZW50U2luZ2xldG9uPlxyXG59XHJcblxyXG5jb25zdCBwcmlzbWE6IFJldHVyblR5cGU8dHlwZW9mIHByaXNtYUNsaWVudFNpbmdsZXRvbj4gPSBnbG9iYWxUaGlzLnByaXNtYUdsb2JhbCA/PyBwcmlzbWFDbGllbnRTaW5nbGV0b24oKVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsVGhpcy5wcmlzbWFHbG9iYWwgPSBwcmlzbWFcclxuXHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWFDbGllbnRTaW5nbGV0b24iLCJwcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hR2xvYmFsIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../packages/db/src/index.ts\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/../../node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"192x192\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91c2VyLWFwcC8uL2FwcC9mYXZpY29uLmljbz8xMzkzIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE5MngxOTJcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/recoil","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5C100xdev%5CCohort%202.0%5CWeb%20codes%5Cxenopay%5CXenoPay%5Capps%5Cuser-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();