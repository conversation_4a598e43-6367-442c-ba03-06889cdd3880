"use client"

import { motion } from "framer-motion";
import Image from "next/image";
import Link from 'next/link';
import { useEffect, useState } from "react";

export function HomePageClient() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    setIsLoaded(true);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const letterVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: (i: number) => ({
      y: 0,
      opacity: 1,
      transition: {
        delay: i * 0.1,
        duration: 0.8,
        ease: "easeOut"
      }
    })
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const text = "Welcome!";

  return (
    <motion.div
      className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Floating Particles Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400/20 rounded-full"
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Cursor Follower */}
      <motion.div
        className="fixed w-6 h-6 bg-blue-500/20 rounded-full pointer-events-none z-50 mix-blend-difference"
        animate={{
          x: mousePosition.x - 12,
          y: mousePosition.y - 12,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
        }}
      />
      {/* Header */}
      <motion.header 
        className="relative bg-white/80 backdrop-blur-md border-b border-gray-200 shadow-sm"
        variants={itemVariants}
      >
        <div className="flex flex-col md:flex-row justify-center md:justify-between px-6 py-4">
          <motion.div 
            className="flex items-center justify-center md:justify-start mx-3 my-2 text-3xl md:text-4xl font-bold text-gray-800"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.img
              width="40"
              height="40"
              src="https://img.icons8.com/ios-filled/50/wallet.png"
              alt="wallet icon"
              className="mr-3"
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              XenoPay
            </span>
          </motion.div>
          
          <motion.div 
            className="flex items-center justify-center md:justify-between space-x-4 mt-2 md:mt-0 font-semibold text-lg md:text-xl"
            variants={itemVariants}
          >
            <span className="font-serif text-gray-600 hidden md:inline">Your Financial Partner</span>
            <motion.img
              className="hidden md:inline"
              width="30"
              height="30"
              src="https://img.icons8.com/doodle/48/handshake--v1.png"
              alt="handshake icon"
              variants={floatingVariants}
              animate="animate"
            />
          </motion.div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="flex flex-col md:flex-row items-center justify-between flex-grow px-6 py-12">
        {/* Left Content */}
        <motion.div 
          className="flex flex-col md:w-1/2 mb-8 md:mb-0 space-y-6"
          variants={itemVariants}
        >
          {/* Animated Welcome Text */}
          <motion.h1 className="text-5xl md:text-7xl font-bold text-gray-800">
            {text.split("").map((char, index) => (
              <motion.span
                key={index}
                className="inline-block"
                variants={letterVariants}
                custom={index}
                whileHover={{ 
                  scale: 1.2, 
                  color: "#3B82F6",
                  transition: { duration: 0.2 }
                }}
              >
                {char}
              </motion.span>
            ))}
          </motion.h1>
          
          {/* Subtitle */}
          <motion.h2 
            className="text-2xl md:text-4xl font-bold text-gray-700 leading-tight"
            variants={itemVariants}
          >
            All Your Payments,{" "}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              One Tap Away
            </span>
          </motion.h2>
          
          {/* Description */}
          <motion.p 
            className="font-serif text-lg md:text-xl text-gray-600 leading-relaxed"
            variants={itemVariants}
          >
            Experience seamless financial transactions with our cutting-edge payment platform. 
            Secure, fast, and reliable.
          </motion.p>
          
          {/* CTA Button */}
          <motion.div variants={itemVariants}>
            <Link href="/api/auth/signin">
              <motion.button 
                className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg overflow-hidden"
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                />
                <span className="relative z-10 flex items-center space-x-2">
                  <span>Get Started</span>
                  <motion.svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </motion.svg>
                </span>
              </motion.button>
            </Link>
          </motion.div>
          
          {/* Feature Pills */}
          <motion.div
            className="flex flex-wrap gap-3 mt-6"
            variants={itemVariants}
          >
            {[
              { name: "Secure", icon: "🔒" },
              { name: "Fast", icon: "⚡" },
              { name: "Reliable", icon: "✅" },
              { name: "24/7 Support", icon: "🛟" }
            ].map((feature, index) => (
              <motion.span
                key={feature.name}
                className="px-4 py-2 bg-white/60 backdrop-blur-sm border border-gray-200 rounded-full text-sm font-medium text-gray-700 flex items-center space-x-2"
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(59, 130, 246, 0.1)",
                  borderColor: "#3B82F6",
                  y: -2
                }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <span>{feature.icon}</span>
                <span>{feature.name}</span>
              </motion.span>
            ))}
          </motion.div>

          {/* Stats Section */}
          <motion.div
            className="grid grid-cols-3 gap-4 mt-8 p-6 bg-white/40 backdrop-blur-sm rounded-2xl border border-gray-200"
            variants={itemVariants}
          >
            {[
              { number: "10K+", label: "Users" },
              { number: "₹50M+", label: "Processed" },
              { number: "99.9%", label: "Uptime" }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 + index * 0.1 }}
              >
                <motion.div
                  className="text-2xl font-bold text-gray-800"
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                >
                  {stat.number}
                </motion.div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
        
        {/* Right Content - Image */}
        <motion.div 
          className="flex items-center justify-center md:w-1/2"
          variants={itemVariants}
        >
          <motion.div
            className="relative"
            animate={{ 
              y: [-5, 5, -5],
              rotate: [0, 1, -1, 0]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl opacity-20"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            />
            <Image
              src="/paytm-bg.webp" 
              alt="Illustration representing payment services"
              width={400}
              height={400}
              className="relative z-10 max-w-full h-auto drop-shadow-2xl"
            />
          </motion.div>
        </motion.div>
      </main>

      {/* Footer */}
      <motion.footer 
        className="bg-white/80 backdrop-blur-md border-t border-gray-200 text-center p-6"
        variants={itemVariants}
      >
        <div className="mx-auto flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 max-w-6xl">
          <motion.div
            whileHover={{ scale: 1.02 }}
          >
            <p className="text-gray-600">&copy; {new Date().getFullYear()} XenoPay</p>
            <p className="font-serif text-gray-500 text-sm">All rights reserved.</p>
          </motion.div>
          
          <motion.div
            whileHover={{ scale: 1.02 }}
          >
            <p className="font-serif text-gray-600">Made by Harsh Kumar Mishra</p>
          </motion.div>
          
          <motion.div 
            className="flex justify-center space-x-4"
            variants={itemVariants}
          >
            <motion.a 
              href="https://github.com/Harshmishra001"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="transition-colors hover:text-blue-600"
            >
              <svg
                height="32"
                viewBox="0 0 72 72"
                width="32"
                xmlns="http://www.w3.org/2000/svg"
                className="fill-current"
              >
                <g fill="none" fillRule="evenodd">
                  <path
                    d="M36,72 L36,72 C55.882251,72 72,55.882251 72,36 L72,36 C72,16.117749 55.882251,-3.65231026e-15 36,0 L36,0 C16.117749,3.65231026e-15 -2.4348735e-15,16.117749 0,36 L0,36 C2.4348735e-15,55.882251 16.117749,72 36,72 Z"
                    fill="#3E75C3"
                  />
                  <path
                    d="M35.9985,12 C22.746,12 12,22.7870921 12,36.096644 C12,46.7406712 18.876,55.7718301 28.4145,58.9584121 C29.6145,59.1797862 30.0525,58.4358488 30.0525,57.7973276 C30.0525,57.2250681 30.0315,55.7100863 30.0195,53.6996482 C23.343,55.1558981 21.9345,50.4693938 21.9345,50.4693938 C20.844,47.6864054 19.2705,46.9454799 19.2705,46.9454799 C17.091,45.4500754 19.4355,45.4801943 19.4355,45.4801943 C21.843,45.6503662 23.1105,47.9634994 23.1105,47.9634994 C25.2525,51.6455377 28.728,50.5823398 30.096,49.9649018 C30.3135,48.4077535 30.9345,47.3460615 31.62,46.7436831 C26.2905,46.1352808 20.688,44.0691228 20.688,34.8361671 C20.688,32.2052792 21.6225,30.0547881 23.1585,28.3696344 C22.911,27.7597262 22.0875,25.3110578 23.3925,21.9934585 C23.3925,21.9934585 25.4085,21.3459017 29.9925,24.4632101 C31.908,23.9285993 33.96,23.6620468 36.0015,23.6515052 C38.04,23.6620468 40.0935,23.9285993 42.0105,24.4632101 C46.5915,21.3459017 48.603,21.9934585 48.603,21.9934585 C49.9125,25.3110578 49.089,27.7597262 48.8415,28.3696344 C50.3805,30.0547881 51.309,32.2052792 51.309,34.8361671 C51.309,44.0917119 45.6975,46.1292571 40.3515,46.7256117 C41.2125,47.4695491 41.9805,48.9393525 41.9805,51.1877301 C41.9805,54.4089489 41.9505,57.0067059 41.9505,57.7973276 C41.9505,58.4418726 42.3825,59.1918338 43.6005,58.9554002 C53.13,55.7627944 60,46.7376593 60,36.096644 C60,22.7870921 49.254,12 35.9985,12"
                    fill="#FFF"
                  />
                </g>
              </svg>
            </motion.a>
            
            <motion.a 
              href="https://www.linkedin.com/in/harsh-mishra001/"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="transition-colors hover:text-blue-600"
            >
              <svg
                enableBackground="new 0 0 32 32"
                height="32px"
                id="Layer_1"
                version="1.0"
                viewBox="0 0 32 32"
                width="32px"
                xmlns="http://www.w3.org/2000/svg"
                className="fill-current"
              >
                <g>
                  <circle clipRule="evenodd" cx="16" cy="16" fill="#007BB5" fillRule="evenodd" r="16" />
                  <g>
                    <rect fill="#FFFFFF" height="14" width="4" x="7" y="11" />
                    <path
                      d="M20.499,11c-2.791,0-3.271,1.018-3.499,2v-2h-4v14h4v-8c0-1.297,0.703-2,2-2c1.266,0,2,0.688,2,2v8h4v-7 C25,14,24.479,11,20.499,11z"
                      fill="#FFFFFF"
                    />
                    <circle cx="9" cy="8" fill="#FFFFFF" r="2" />
                  </g>
                </g>
              </svg>
            </motion.a>
          </motion.div>
        </div>
      </motion.footer>
    </motion.div>
  );
}
