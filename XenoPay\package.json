{"name": "week-18", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "db:generate": "cd packages/db && npx prisma generate cd ../..", "start-user-app": "npm run dev && cd ../.."}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "prettier": "^3.2.5", "turbo": "latest"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.2.4", "workspaces": ["apps/*", "packages/*"], "dependencies": {"tailwind": "^4.0.0"}}