"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/HomePageClient.tsx":
/*!***************************************!*\
  !*** ./components/HomePageClient.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePageClient: function() { return /* binding */ HomePageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ HomePageClient auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HomePageClient() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setIsLoaded(true);\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                duration: 0.5,\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            y: 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const letterVariants = {\n        hidden: {\n            y: 50,\n            opacity: 0\n        },\n        visible: (i)=>({\n                y: 0,\n                opacity: 1,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.8,\n                    ease: \"easeOut\"\n                }\n            })\n    };\n    const floatingVariants = {\n        animate: {\n            y: [\n                -10,\n                10,\n                -10\n            ],\n            transition: {\n                duration: 3,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }\n    };\n    const text = \"Welcome!\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute w-2 h-2 bg-blue-400/20 rounded-full\",\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            opacity: [\n                                0,\n                                1,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: Math.random() * 10 + 10,\n                            repeat: Infinity,\n                            delay: Math.random() * 5\n                        },\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"fixed w-6 h-6 bg-blue-500/20 rounded-full pointer-events-none z-50 mix-blend-difference\",\n                animate: {\n                    x: mousePosition.x - 12,\n                    y: mousePosition.y - 12\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 500,\n                    damping: 28\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n                className: \"relative bg-white/80 backdrop-blur-md border-b border-gray-200 shadow-sm\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-center md:justify-between px-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-start mx-3 my-2 text-3xl md:text-4xl font-bold text-gray-800\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    width: \"40\",\n                                    height: \"40\",\n                                    src: \"https://img.icons8.com/ios-filled/50/wallet.png\",\n                                    alt: \"wallet icon\",\n                                    className: \"mr-3\",\n                                    animate: {\n                                        rotate: [\n                                            0,\n                                            5,\n                                            -5,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"XenoPay\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex items-center justify-center md:justify-between space-x-4 mt-2 md:mt-0 font-semibold text-lg md:text-xl\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-serif text-gray-600 hidden md:inline\",\n                                    children: \"Your Financial Partner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.img, {\n                                    className: \"hidden md:inline\",\n                                    width: \"30\",\n                                    height: \"30\",\n                                    src: \"https://img.icons8.com/doodle/48/handshake--v1.png\",\n                                    alt: \"handshake icon\",\n                                    variants: floatingVariants,\n                                    animate: \"animate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col md:flex-row items-center justify-between flex-grow px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex flex-col md:w-1/2 mb-8 md:mb-0 space-y-6\",\n                        variants: itemVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-800\",\n                                children: text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"inline-block\",\n                                        variants: letterVariants,\n                                        custom: index,\n                                        whileHover: {\n                                            scale: 1.2,\n                                            color: \"#3B82F6\",\n                                            transition: {\n                                                duration: 0.2\n                                            }\n                                        },\n                                        children: char\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-2xl md:text-4xl font-bold text-gray-700 leading-tight\",\n                                variants: itemVariants,\n                                children: [\n                                    \"All Your Payments,\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"One Tap Away\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"font-serif text-lg md:text-xl text-gray-600 leading-relaxed\",\n                                variants: itemVariants,\n                                children: \"Experience seamless financial transactions with our cutting-edge payment platform. Secure, fast, and reliable.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                variants: itemVariants,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/api/auth/signin\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\"\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Get Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.svg, {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        animate: {\n                                                            x: [\n                                                                0,\n                                                                5,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1.5,\n                                                            repeat: Infinity\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"flex flex-wrap gap-3 mt-6\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        name: \"Secure\",\n                                        icon: \"\\uD83D\\uDD12\"\n                                    },\n                                    {\n                                        name: \"Fast\",\n                                        icon: \"⚡\"\n                                    },\n                                    {\n                                        name: \"Reliable\",\n                                        icon: \"✅\"\n                                    },\n                                    {\n                                        name: \"24/7 Support\",\n                                        icon: \"\\uD83D\\uDEDF\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"px-4 py-2 bg-white/60 backdrop-blur-sm border border-gray-200 rounded-full text-sm font-medium text-gray-700 flex items-center space-x-2\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n                                            borderColor: \"#3B82F6\",\n                                            y: -2\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"grid grid-cols-3 gap-4 mt-8 p-6 bg-white/40 backdrop-blur-sm rounded-2xl border border-gray-200\",\n                                variants: itemVariants,\n                                children: [\n                                    {\n                                        number: \"10K+\",\n                                        label: \"Users\"\n                                    },\n                                    {\n                                        number: \"₹50M+\",\n                                        label: \"Processed\"\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Uptime\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"text-center\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.8 + index * 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    delay: index * 0.5\n                                                },\n                                                children: stat.number\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, stat.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"flex items-center justify-center md:w-1/2\",\n                        variants: itemVariants,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"relative\",\n                            animate: {\n                                y: [\n                                    -5,\n                                    5,\n                                    -5\n                                ],\n                                rotate: [\n                                    0,\n                                    1,\n                                    -1,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl opacity-20\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/paytm-bg.webp\",\n                                    alt: \"Illustration representing payment services\",\n                                    width: 400,\n                                    height: 400,\n                                    className: \"relative z-10 max-w-full h-auto drop-shadow-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.footer, {\n                className: \"bg-white/80 backdrop-blur-md border-t border-gray-200 text-center p-6\",\n                variants: itemVariants,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 max-w-6xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" XenoPay\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-serif text-gray-500 text-sm\",\n                                    children: \"All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-serif text-gray-600\",\n                                children: \"Made by Harsh Kumar Mishra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex justify-center space-x-4\",\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://github.com/Harshmishra001\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        height: \"32\",\n                                        viewBox: \"0 0 72 72\",\n                                        width: \"32\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            fill: \"none\",\n                                            fillRule: \"evenodd\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M36,72 L36,72 C55.882251,72 72,55.882251 72,36 L72,36 C72,16.117749 55.882251,-3.65231026e-15 36,0 L36,0 C16.117749,3.65231026e-15 -2.4348735e-15,16.117749 0,36 L0,36 C2.4348735e-15,55.882251 16.117749,72 36,72 Z\",\n                                                    fill: \"#3E75C3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M35.9985,12 C22.746,12 12,22.7870921 12,36.096644 C12,46.7406712 18.876,55.7718301 28.4145,58.9584121 C29.6145,59.1797862 30.0525,58.4358488 30.0525,57.7973276 C30.0525,57.2250681 30.0315,55.7100863 30.0195,53.6996482 C23.343,55.1558981 21.9345,50.4693938 21.9345,50.4693938 C20.844,47.6864054 19.2705,46.9454799 19.2705,46.9454799 C17.091,45.4500754 19.4355,45.4801943 19.4355,45.4801943 C21.843,45.6503662 23.1105,47.9634994 23.1105,47.9634994 C25.2525,51.6455377 28.728,50.5823398 30.096,49.9649018 C30.3135,48.4077535 30.9345,47.3460615 31.62,46.7436831 C26.2905,46.1352808 20.688,44.0691228 20.688,34.8361671 C20.688,32.2052792 21.6225,30.0547881 23.1585,28.3696344 C22.911,27.7597262 22.0875,25.3110578 23.3925,21.9934585 C23.3925,21.9934585 25.4085,21.3459017 29.9925,24.4632101 C31.908,23.9285993 33.96,23.6620468 36.0015,23.6515052 C38.04,23.6620468 40.0935,23.9285993 42.0105,24.4632101 C46.5915,21.3459017 48.603,21.9934585 48.603,21.9934585 C49.9125,25.3110578 49.089,27.7597262 48.8415,28.3696344 C50.3805,30.0547881 51.309,32.2052792 51.309,34.8361671 C51.309,44.0917119 45.6975,46.1292571 40.3515,46.7256117 C41.2125,47.4695491 41.9805,48.9393525 41.9805,51.1877301 C41.9805,54.4089489 41.9505,57.0067059 41.9505,57.7973276 C41.9505,58.4418726 42.3825,59.1918338 43.6005,58.9554002 C53.13,55.7627944 60,46.7376593 60,36.096644 C60,22.7870921 49.254,12 35.9985,12\",\n                                                    fill: \"#FFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"https://www.linkedin.com/in/harsh-mishra001/\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"transition-colors hover:text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        enableBackground: \"new 0 0 32 32\",\n                                        height: \"32px\",\n                                        id: \"Layer_1\",\n                                        version: \"1.0\",\n                                        viewBox: \"0 0 32 32\",\n                                        width: \"32px\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"fill-current\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    clipRule: \"evenodd\",\n                                                    cx: \"16\",\n                                                    cy: \"16\",\n                                                    fill: \"#007BB5\",\n                                                    fillRule: \"evenodd\",\n                                                    r: \"16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                            fill: \"#FFFFFF\",\n                                                            height: \"14\",\n                                                            width: \"4\",\n                                                            x: \"7\",\n                                                            y: \"11\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20.499,11c-2.791,0-3.271,1.018-3.499,2v-2h-4v14h4v-8c0-1.297,0.703-2,2-2c1.266,0,2,0.688,2,2v8h4v-7 C25,14,24.479,11,20.499,11z\",\n                                                            fill: \"#FFFFFF\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"9\",\n                                                            cy: \"8\",\n                                                            fill: \"#FFFFFF\",\n                                                            r: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\100xdev\\\\Cohort 2.0\\\\Web codes\\\\xenopay\\\\XenoPay\\\\apps\\\\user-app\\\\components\\\\HomePageClient.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePageClient, \"+iAxKq/g7R0ug7rIK5a7synY29A=\");\n_c = HomePageClient;\nvar _c;\n$RefreshReg$(_c, \"HomePageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomePageClient.tsx\n"));

/***/ })

});