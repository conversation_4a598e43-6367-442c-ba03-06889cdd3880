{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx", "./appbar": "./src/Appbar.tsx", "./center": "./src/Center.tsx", "./select": "./src/Select.tsx", "./textinput": "./src/TextInput.tsx", "./card2": "./src/card2.tsx", "./card3": "./src/card3.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/eslint": "^8.56.5", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "react": "^18.2.0", "typescript": "^5.3.3"}}