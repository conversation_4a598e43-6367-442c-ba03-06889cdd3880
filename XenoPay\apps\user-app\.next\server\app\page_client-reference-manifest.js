globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./provider.tsx":{"*":{"id":"(ssr)/./provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\provider.tsx":{"id":"(app-pages-browser)/./provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\app\\(dashboard)\\layout.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\100xdev\\Cohort 2.0\\Web codes\\xenopay\\XenoPay\\apps\\user-app\\app\\(dashboard)\\layout":["static/css/app/(dashboard)/layout.css"]}}