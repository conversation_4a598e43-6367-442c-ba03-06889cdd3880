/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oidc-token-hash";
exports.ids = ["vendor-chunks/oidc-token-hash"];
exports.modules = {

/***/ "(action-browser)/../../node_modules/oidc-token-hash/lib/index.js":
/*!*******************************************************!*\
  !*** ../../node_modules/oidc-token-hash/lib/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst shake256 = __webpack_require__(/*! ./shake256 */ \"(action-browser)/../../node_modules/oidc-token-hash/lib/shake256.js\");\n\nlet encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input) => input.toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input) => fromBase64(input.toString('base64'));\n}\n\n/** SPECIFICATION\n * Its (_hash) value is the base64url encoding of the left-most half of the hash of the octets of\n * the ASCII representation of the token value, where the hash algorithm used is the hash algorithm\n * used in the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is\n * RS256, hash the token value with SHA-256, then take the left-most 128 bits and base64url encode\n * them. The _hash value is a case sensitive string.\n */\n\n/**\n * @name getHash\n * @api private\n *\n * returns the sha length based off the JOSE alg heade value, defaults to sha256\n *\n * @param token {String} token value to generate the hash from\n * @param alg {String} ID Token JOSE header alg value (i.e. RS256, HS384, ES512, PS256)\n * @param [crv] {String} For EdDSA the curve decides what hash algorithm is used. Required for EdDSA\n */\nfunction getHash(alg, crv) {\n  switch (alg) {\n    case 'HS256':\n    case 'RS256':\n    case 'PS256':\n    case 'ES256':\n    case 'ES256K':\n      return createHash('sha256');\n\n    case 'HS384':\n    case 'RS384':\n    case 'PS384':\n    case 'ES384':\n      return createHash('sha384');\n\n    case 'HS512':\n    case 'RS512':\n    case 'PS512':\n    case 'ES512':\n      return createHash('sha512');\n\n    case 'EdDSA':\n      switch (crv) {\n        case 'Ed25519':\n          return createHash('sha512');\n        case 'Ed448':\n          if (!shake256) {\n            throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n          }\n\n          return createHash('shake256', { outputLength: 114 });\n        default:\n          throw new TypeError('unrecognized or invalid EdDSA curve provided');\n      }\n\n    default:\n      throw new TypeError('unrecognized or invalid JWS algorithm provided');\n  }\n}\n\nfunction generate(token, alg, crv) {\n  const digest = getHash(alg, crv).update(token).digest();\n  return encode(digest.slice(0, digest.length / 2));\n}\n\nfunction validate(names, actual, source, alg, crv) {\n  if (typeof names.claim !== 'string' || !names.claim) {\n    throw new TypeError('names.claim must be a non-empty string');\n  }\n\n  if (typeof names.source !== 'string' || !names.source) {\n    throw new TypeError('names.source must be a non-empty string');\n  }\n\n  assert(typeof actual === 'string' && actual, `${names.claim} must be a non-empty string`);\n  assert(typeof source === 'string' && source, `${names.source} must be a non-empty string`);\n\n  let expected;\n  let msg;\n  try {\n    expected = generate(source, alg, crv);\n  } catch (err) {\n    msg = format('%s could not be validated (%s)', names.claim, err.message);\n  }\n\n  msg = msg || format('%s mismatch, expected %s, got: %s', names.claim, expected, actual);\n\n  assert.equal(expected, actual, msg);\n}\n\nmodule.exports = {\n  validate,\n  generate,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/../../node_modules/oidc-token-hash/lib/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/oidc-token-hash/lib/index.js":
/*!*******************************************************!*\
  !*** ../../node_modules/oidc-token-hash/lib/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst shake256 = __webpack_require__(/*! ./shake256 */ \"(rsc)/../../node_modules/oidc-token-hash/lib/shake256.js\");\n\nlet encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input) => input.toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input) => fromBase64(input.toString('base64'));\n}\n\n/** SPECIFICATION\n * Its (_hash) value is the base64url encoding of the left-most half of the hash of the octets of\n * the ASCII representation of the token value, where the hash algorithm used is the hash algorithm\n * used in the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is\n * RS256, hash the token value with SHA-256, then take the left-most 128 bits and base64url encode\n * them. The _hash value is a case sensitive string.\n */\n\n/**\n * @name getHash\n * @api private\n *\n * returns the sha length based off the JOSE alg heade value, defaults to sha256\n *\n * @param token {String} token value to generate the hash from\n * @param alg {String} ID Token JOSE header alg value (i.e. RS256, HS384, ES512, PS256)\n * @param [crv] {String} For EdDSA the curve decides what hash algorithm is used. Required for EdDSA\n */\nfunction getHash(alg, crv) {\n  switch (alg) {\n    case 'HS256':\n    case 'RS256':\n    case 'PS256':\n    case 'ES256':\n    case 'ES256K':\n      return createHash('sha256');\n\n    case 'HS384':\n    case 'RS384':\n    case 'PS384':\n    case 'ES384':\n      return createHash('sha384');\n\n    case 'HS512':\n    case 'RS512':\n    case 'PS512':\n    case 'ES512':\n      return createHash('sha512');\n\n    case 'EdDSA':\n      switch (crv) {\n        case 'Ed25519':\n          return createHash('sha512');\n        case 'Ed448':\n          if (!shake256) {\n            throw new TypeError('Ed448 *_hash calculation is not supported in your Node.js runtime version');\n          }\n\n          return createHash('shake256', { outputLength: 114 });\n        default:\n          throw new TypeError('unrecognized or invalid EdDSA curve provided');\n      }\n\n    default:\n      throw new TypeError('unrecognized or invalid JWS algorithm provided');\n  }\n}\n\nfunction generate(token, alg, crv) {\n  const digest = getHash(alg, crv).update(token).digest();\n  return encode(digest.slice(0, digest.length / 2));\n}\n\nfunction validate(names, actual, source, alg, crv) {\n  if (typeof names.claim !== 'string' || !names.claim) {\n    throw new TypeError('names.claim must be a non-empty string');\n  }\n\n  if (typeof names.source !== 'string' || !names.source) {\n    throw new TypeError('names.source must be a non-empty string');\n  }\n\n  assert(typeof actual === 'string' && actual, `${names.claim} must be a non-empty string`);\n  assert(typeof source === 'string' && source, `${names.source} must be a non-empty string`);\n\n  let expected;\n  let msg;\n  try {\n    expected = generate(source, alg, crv);\n  } catch (err) {\n    msg = format('%s could not be validated (%s)', names.claim, err.message);\n  }\n\n  msg = msg || format('%s mismatch, expected %s, got: %s', names.claim, expected, actual);\n\n  assert.equal(expected, actual, msg);\n}\n\nmodule.exports = {\n  validate,\n  generate,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/oidc-token-hash/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/../../node_modules/oidc-token-hash/lib/shake256.js":
/*!**********************************************************!*\
  !*** ../../node_modules/oidc-token-hash/lib/shake256.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst [major, minor] = process.version.substring(1).split('.').map((x) => parseInt(x, 10));\nconst xofOutputLength = major > 12 || (major === 12 && minor >= 8);\nconst shake256 = xofOutputLength && crypto.getHashes().includes('shake256');\n\nmodule.exports = shake256;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvb2lkYy10b2tlbi1oYXNoL2xpYi9zaGFrZTI1Ni5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL3VzZXItYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9vaWRjLXRva2VuLWhhc2gvbGliL3NoYWtlMjU2LmpzP2ZjNTAiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbmNvbnN0IFttYWpvciwgbWlub3JdID0gcHJvY2Vzcy52ZXJzaW9uLnN1YnN0cmluZygxKS5zcGxpdCgnLicpLm1hcCgoeCkgPT4gcGFyc2VJbnQoeCwgMTApKTtcbmNvbnN0IHhvZk91dHB1dExlbmd0aCA9IG1ham9yID4gMTIgfHwgKG1ham9yID09PSAxMiAmJiBtaW5vciA+PSA4KTtcbmNvbnN0IHNoYWtlMjU2ID0geG9mT3V0cHV0TGVuZ3RoICYmIGNyeXB0by5nZXRIYXNoZXMoKS5pbmNsdWRlcygnc2hha2UyNTYnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzaGFrZTI1NjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/../../node_modules/oidc-token-hash/lib/shake256.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/oidc-token-hash/lib/shake256.js":
/*!**********************************************************!*\
  !*** ../../node_modules/oidc-token-hash/lib/shake256.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst [major, minor] = process.version.substring(1).split('.').map((x) => parseInt(x, 10));\nconst xofOutputLength = major > 12 || (major === 12 && minor >= 8);\nconst shake256 = xofOutputLength && crypto.getHashes().includes('shake256');\n\nmodule.exports = shake256;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL29pZGMtdG9rZW4taGFzaC9saWIvc2hha2UyNTYuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLHNCQUFROztBQUUvQjtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91c2VyLWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvb2lkYy10b2tlbi1oYXNoL2xpYi9zaGFrZTI1Ni5qcz84ZDAxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5jb25zdCBbbWFqb3IsIG1pbm9yXSA9IHByb2Nlc3MudmVyc2lvbi5zdWJzdHJpbmcoMSkuc3BsaXQoJy4nKS5tYXAoKHgpID0+IHBhcnNlSW50KHgsIDEwKSk7XG5jb25zdCB4b2ZPdXRwdXRMZW5ndGggPSBtYWpvciA+IDEyIHx8IChtYWpvciA9PT0gMTIgJiYgbWlub3IgPj0gOCk7XG5jb25zdCBzaGFrZTI1NiA9IHhvZk91dHB1dExlbmd0aCAmJiBjcnlwdG8uZ2V0SGFzaGVzKCkuaW5jbHVkZXMoJ3NoYWtlMjU2Jyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2hha2UyNTY7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/oidc-token-hash/lib/shake256.js\n");

/***/ })

};
;