
import { getServerSession } from "next-auth";
import { redirect } from 'next/navigation';
import { HomePageClient } from "../components/HomePageClient";
import './globals.css';
import { authOptions } from "./lib/auth";

export default async function Page() {
  const session = await getServerSession(authOptions);

  if (session?.user) {
    redirect('/account-details');
  }

  return <HomePageClient />;
}