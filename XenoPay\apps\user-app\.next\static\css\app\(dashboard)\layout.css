/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!../../packages/ui/src/style.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
@keyframes fadeIn {
    0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1); 
      }
      100% {
        transform: scale(1); 
      }
  }
  
  .animate-fadeIn {
    animation: fadeIn 3s ease-in-out infinite;
 
  }
