!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,function(t){"use strict";function e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function n(t){let e;return()=>(void 0===e&&(e=t()),e)}const i=t=>t,s=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},o=t=>1e3*t,a=t=>t/1e3;function r(t,e){return n=t,Array.isArray(n)&&"number"!=typeof n[0]?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t;var n}const l=(t,e,n)=>t+(e-t)*n,u=2e4;function h(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(function(t){let e=0,n=t.next(e);for(;!n.done&&e<u;)e+=50,n=t.next(e);return e>=u?1/0:e}(i),u);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:a(s)}}function c(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const o=s(0,e,i);t.push(l(n,1,o))}}function f(t){const e=[0];return c(e,t.length-1),e}const m=t=>null!==t;class d{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}function p(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const y=t=>t.startsWith("--");const g=n(()=>void 0!==window.ScrollTimeline),A={};function b(t,e){const i=n(t);return()=>A[e]??i()}const T=b(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),v=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:v([0,.65,.55,1]),circOut:v([.55,0,1,.45]),backIn:v([.31,.01,.66,-.59]),backOut:v([.33,1.53,.69,.99])};function w(t,e){return t?"function"==typeof t?T()?((t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`})(t,e):"ease-out":(t=>Array.isArray(t)&&"number"==typeof t[0])(t)?v(t):Array.isArray(t)?t.map(t=>w(t,e)||M.easeOut):M[t]:void 0}function k(t){return"function"==typeof t&&"applyToOptions"in t}class x extends d{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:a,onComplete:r}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return k(t)&&T()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:a="loop",ease:r="easeOut",times:l}={},u){const h={[e]:n};l&&(h.offset=l);const c=w(r,s);Array.isArray(c)&&(h.easing=c);const f={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};return u&&(f.pseudoElement=u),t.animate(h,f)}(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=function(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(m),a=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return a&&void 0!==i?i:o[a]}(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){y(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}r?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return a(Number(t))}get time(){return a(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=o(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&g()?(this.animation.timeline=t,i):e(this)}}class E{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class O extends E{then(t,e){return this.finished.finally(t).then(()=>{})}}const S=new WeakMap,R=(t,e="")=>`${t}:${e}`;function F(t){const e=S.get(t)||new Map;return S.set(t,e),e}function W(t,e){return t?.[e]??t?.default??t}const P=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function B(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&P.has(e)&&(t[n]=t[n]+"px")}function $(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}function L(t,e){const n=window.getComputedStyle(t);return y(e)?n.getPropertyValue(e):n[e]}const V=t=>Boolean(t&&t.getVelocity);function I(t,e,n,i){return"string"==typeof t&&function(t){return"object"==typeof t&&!Array.isArray(t)}(e)?$(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function N(t,e,n){return t*(e+1)}function C(t,e,n,i){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):i.get(e)??t}function K(t,n,i,s,o,a){!function(t,n,i){for(let s=0;s<t.length;s++){const o=t[s];o.at>n&&o.at<i&&(e(t,o),s--)}}(t,o,a);for(let e=0;e<n.length;e++)t.push({value:n[e],at:l(o,a,s[e]),easing:r(i,e)})}function j(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function q(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function _(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function z(t,e){return e[t]||(e[t]=[]),e[t]}function D(t){return Array.isArray(t)?t:[t]}function H(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const X=t=>"number"==typeof t,Y=t=>t.every(X);function G(t,e,n,i){const s=$(t,i),a=s.length,r=[];for(let t=0;t<a;t++){const i=s[t],l={...n};"function"==typeof l.delay&&(l.delay=l.delay(t,a));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const s={...W(l,t)};s.duration&&(s.duration=o(s.duration)),s.delay&&(s.delay=o(s.delay));const a=F(i),u=R(t,s.pseudoElement||""),h=a.get(u);h&&h.stop(),r.push({map:a,key:u,unresolvedKeyframes:n,options:{...s,element:i,name:t,allowFlatten:!l.type&&!l.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:n}=r[t],{element:i,name:s,pseudoElement:o}=n;o||null!==e[0]||(e[0]=L(i,s)),p(e),B(e,s),!o&&e.length<2&&e.unshift(L(i,s)),n.keyframes=e}const l=[];for(let t=0;t<r.length;t++){const{map:e,key:n,options:i}=r[t],s=new x(i);e.set(n,s),s.finished.finally(()=>e.delete(n)),l.push(s)}return l}const J=(t=>function(e,n,i){return new O(G(e,n,i,t))})();t.animate=J,t.animateSequence=function(t,e){const n=[];return function(t,{defaultTransition:e={},...n}={},i,a){const l=e.duration||.3,u=new Map,m=new Map,d={},p=new Map;let y=0,g=0,A=0;for(let n=0;n<t.length;n++){const s=t[n];if("string"==typeof s){p.set(s,g);continue}if(!Array.isArray(s)){p.set(s.name,C(g,s.at,y,p));continue}let[u,b,T={}]=s;void 0!==T.at&&(g=C(g,T.at,y,p));let v=0;const M=(t,n,i,s=0,u=0)=>{const m=D(t),{delay:d=0,times:p=f(m),type:y="keyframes",repeat:b,repeatType:T,repeatDelay:M=0,...w}=n;let{ease:x=e.ease||"easeOut",duration:E}=n;const O="function"==typeof d?d(s,u):d,S=m.length,R=k(y)?y:a?.[y||"keyframes"];if(S<=2&&R){let t=100;if(2===S&&Y(m)){const e=m[1]-m[0];t=Math.abs(e)}const e={...w};void 0!==E&&(e.duration=o(E));const n=h(e,t,R);x=n.ease,E=n.duration}E??(E=l);const F=g+O;1===p.length&&0===p[0]&&(p[1]=1);const W=p.length-m.length;if(W>0&&c(p,W),1===m.length&&m.unshift(null),b){E=N(E,b);const t=[...m],e=[...p];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<b;i++){m.push(...t);for(let s=0;s<t.length;s++)p.push(e[s]+(i+1)),x.push(0===s?"linear":r(n,s-1))}j(p,b)}const P=F+E;K(i,m,x,p,F,P),v=Math.max(O+E,v),A=Math.max(P,A)};if(V(u))M(b,T,z("default",_(u,m)));else{const t=I(u,b,i,d),e=t.length;for(let n=0;n<e;n++){const i=_(t[n],m);for(const t in b)M(b[t],H(T,t),z(t,i),n,e)}}y=g,g+=v}return m.forEach((t,i)=>{for(const o in t){const a=t[o];a.sort(q);const r=[],l=[],h=[];for(let t=0;t<a.length;t++){const{at:e,value:n,easing:i}=a[t];r.push(n),l.push(s(0,A,e)),h.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),r.unshift(r[0]),h.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),r.push(null)),u.has(i)||u.set(i,{keyframes:{},transition:{}});const c=u.get(i);c.keyframes[o]=r,c.transition[o]={...e,duration:A,ease:h,times:l,...n}}}),u}(t,e).forEach(({keyframes:t,transition:e},i)=>{n.push(...G(i,t,e))}),new O(n)}});
