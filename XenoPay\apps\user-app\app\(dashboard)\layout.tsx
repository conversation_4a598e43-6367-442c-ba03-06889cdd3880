"use client"

import { AppbarClient } from "../../components/AppbarClient";
import { SidebarItem } from "../../components/SidebarItem";
import { useState } from "react";


export default function Layout({ children }: { children: React.ReactNode }): JSX.Element {
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);

  return (
    <div>
      {/* Top App Bar */}
      <AppbarClient />

      {/* Bottom Menu Icon for Mobile, directly below AppbarClient */}
      <div className="md:hidden bg-white border-b border-slate-300">
        <button
          onClick={() => setShowMobileSidebar(!showMobileSidebar)}
          className="w-full text-center py-2"
        >
        <img width="50" height="50" src="https://img.icons8.com/ios-filled/50/menu--v6.png" alt="menu--v6"/>
        </button>

        {showMobileSidebar && (
          <div className=" py-2 bg-white shadow-lg">
            <SidebarItem href={"/account-details"} icon={<BalanceIcon />} title="Account" />
            <SidebarItem href={"/transfer"} icon={<TransferIcon />} title="Transfer" />
            <SidebarItem href={"/transactions"} icon={<TransactionsIcon />} title="Transactions" />
            <SidebarItem href={"/p2p"} icon={<P2PTransferIcon />} title="P2P" />
          </div>
        )}
      </div>

      {/* Main Layout */}
      <div className="flex">
        {/* Sidebar for Desktop Screens */}
        <div className="hidden md:flex w-72 border-r border-slate-300 min-h-screen mr-4 pt-28">
          <div>
            <SidebarItem href={"/account-details"} icon={<BalanceIcon />} title=" Account-Info" />
            <SidebarItem href={"/transfer"} icon={<TransferIcon />} title="Transfer" />
            <SidebarItem href={"/transactions"} icon={<TransactionsIcon />} title="Transactions" />
            <SidebarItem href={"/p2p"} icon={<P2PTransferIcon />} title="P2P Transfer" />
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1">
          {children}
        </div>
      </div>
    </div>
  );
}



 function TransferIcon() {
    return <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" className="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
  </svg>
}

 function TransactionsIcon() {
    return <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" className="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
  
}

 function P2PTransferIcon() {
  return <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" className="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 19.5 15-15m0 0H8.25m11.25 0v11.25" />
  </svg>
}

function BalanceIcon(){
   return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-6">
  <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM9 7.5A.75.75 0 0 0 9 9h1.5c.98 0 1.813.626 2.122 1.5H9A.75.75 0 0 0 9 12h3.622a2.251 2.251 0 0 1-2.122 1.5H9a.75.75 0 0 0-.53 1.28l3 3a.75.75 0 1 0 1.06-1.06L10.8 14.988A3.752 3.752 0 0 0 14.175 12H15a.75.75 0 0 0 0-1.5h-.825A3.733 3.733 0 0 0 13.5 9H15a.75.75 0 0 0 0-1.5H9Z" clip-rule="evenodd" />
</svg>

}