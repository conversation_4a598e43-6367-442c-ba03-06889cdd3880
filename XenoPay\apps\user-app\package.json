{"name": "user-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@prisma/client": "^5.17.0", "@repo/db": "*", "@repo/store": "*", "@repo/ui": "*", "@types/bcrypt": "^5.0.2", "bcrypt": "^5.1.1", "framer-motion": "^12.23.1", "next": "^14.1.1", "next-auth": "^4.24.7", "prisma": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@next/eslint-plugin-next": "^14.1.1", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/eslint": "^8.56.5", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}