self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"a9f044710b39d52a1a3fc5b9b3bc671afe611275\": {\n      \"workers\": {\n        \"app/(dashboard)/transfer/page\": \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5C100xdev%5C%5CCohort%202.0%5C%5CWeb%20codes%5C%5Cxenopay%5C%5CXenoPay%5C%5Capps%5C%5Cuser-app%5C%5Capp%5C%5Clib%5C%5Cactions%5C%5CcreateOnrampTransaction.ts%22%2C%5B%22createOnRampTransaction%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(dashboard)/transfer/page\": \"action-browser\"\n      }\n    },\n    \"28859cbbefa47d1555f9c5c7d00a5f5d6d5d2628\": {\n      \"workers\": {\n        \"app/(dashboard)/p2p/page\": \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5C100xdev%5C%5CCohort%202.0%5C%5CWeb%20codes%5C%5Cxenopay%5C%5CXenoPay%5C%5Capps%5C%5Cuser-app%5C%5Capp%5C%5Clib%5C%5Cactions%5C%5Cp2pTransferbackend.tsx%22%2C%5B%22p2pTransferBackend%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(dashboard)/p2p/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"0Ql42Mt0OIUnNZGi8nGNykst79SNDvh1GGYEmvNLZ9I=\"\n}"